import { CertificateCategory } from '@/types/certificate';

/**
 * 分类配置文件
 * 用于控制哪些分类在网站上显示和可用
 */

export interface CategoryConfig {
  category: CertificateCategory;
  enabled: boolean;
  reason?: string; // 禁用原因
  order: number; // 显示顺序
}

/**
 * 分类启用配置
 * 可以通过修改这个配置来控制分类的显示
 */
export const CATEGORY_CONFIGS: CategoryConfig[] = [
  {
    category: CertificateCategory.COMPLETION,
    enabled: true,
    order: 1 // 放在第一位
  },
  {
    category: CertificateCategory.ACHIEVEMENT,
    enabled: true,
    order: 2
  },
  {
    category: CertificateCategory.PARTICIPATION,
    enabled: true,
    order: 3
  },
  {
    category: CertificateCategory.EXCELLENCE,
    enabled: true,
    order: 4
  }
];

/**
 * 获取分类配置
 */
export function getCategoryConfig(category: CertificateCategory): CategoryConfig | undefined {
  return CATEGORY_CONFIGS.find(config => config.category === category);
}

/**
 * 检查分类是否启用
 */
export function isCategoryEnabled(category: CertificateCategory): boolean {
  const config = getCategoryConfig(category);
  return config?.enabled ?? false;
}

/**
 * 获取启用的分类列表（按顺序排序）
 */
export function getEnabledCategories(): CertificateCategory[] {
  return CATEGORY_CONFIGS
    .filter(config => config.enabled)
    .sort((a, b) => a.order - b.order)
    .map(config => config.category);
}

/**
 * 获取所有分类列表（按顺序排序）
 */
export function getAllCategoriesOrdered(): CertificateCategory[] {
  return CATEGORY_CONFIGS
    .sort((a, b) => a.order - b.order)
    .map(config => config.category);
}

/**
 * 开发环境配置
 * 在开发环境中可以显示所有分类（包括disabled的）用于测试
 */
export const DEV_CONFIG = {
  showDisabledCategories: process.env.NODE_ENV === 'development',
  showDisabledReason: true
};
