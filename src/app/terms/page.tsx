import { Metadata } from 'next';
import PageLayout from '@/components/layout/PageLayout';

export const metadata: Metadata = {
  title: 'Terms of Service | Certificate Maker',
  description: 'Terms of Service for CertificateMaker.App - Read our terms and conditions for using our free online certificate maker.',
  robots: {
    index: true,
    follow: true,
  },
};

export default function TermsOfServicePage() {
  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-16 max-w-4xl">
        <div className="prose prose-lg max-w-none">
          <h1 className="text-4xl font-bold text-gray-900 mb-8">Terms of Service</h1>
          
          <p className="text-lg text-gray-600 mb-8">
            <strong>Effective Date:</strong> January 1, 2024<br />
            <strong>Last Updated:</strong> January 1, 2024
          </p>

          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-700 leading-relaxed">
                By accessing and using CertificateMaker.App (&quot;the Service&quot;), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">2. Description of Service</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                CertificateMaker.App is a free online service that allows users to create professional certificates using pre-designed templates. The service includes:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Access to professional certificate templates</li>
                <li>Customization tools for certificate content</li>
                <li>PDF generation and download capabilities</li>
                <li>No registration or account creation required</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">3. User Responsibilities</h2>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3">3.1 Appropriate Use</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                You agree to use the Service only for lawful purposes and in accordance with these Terms. You agree not to:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2 mb-4">
                <li>Create certificates with false, misleading, or fraudulent information</li>
                <li>Use the Service to create certificates that violate any laws or regulations</li>
                <li>Attempt to reverse engineer, modify, or distribute our templates without permission</li>
                <li>Use the Service in any way that could damage, disable, or impair the website</li>
                <li>Attempt to gain unauthorized access to any part of the Service</li>
              </ul>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">3.2 Content Accuracy</h3>
              <p className="text-gray-700 leading-relaxed">
                You are solely responsible for the accuracy and appropriateness of the content you include in your certificates. We do not verify the truthfulness of certificate content and are not responsible for any consequences arising from inaccurate or inappropriate content.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">4. Intellectual Property Rights</h2>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3">4.1 Our Rights</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                The Service, including all templates, designs, text, graphics, and software, is owned by us and is protected by copyright, trademark, and other intellectual property laws.
              </p>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">4.2 Your Rights</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                You retain ownership of the content you create using our Service. Once you generate a certificate, you have the right to:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Use the certificate for its intended purpose</li>
                <li>Print and distribute the certificate as needed</li>
                <li>Modify the content for your specific needs</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">5. Privacy and Data</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                <strong>Important:</strong> We do not store or retain any certificate content you create. All processing happens locally in your browser, ensuring your data remains private and secure.
              </p>
              <p className="text-gray-700 leading-relaxed">
                For more information about how we handle your data, please review our Privacy Policy.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">6. Service Availability</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                We strive to maintain the Service&apos;s availability, but we do not guarantee uninterrupted access. The Service may be temporarily unavailable due to:
              </p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>Scheduled maintenance</li>
                <li>Technical issues or server problems</li>
                <li>Circumstances beyond our control</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">7. Disclaimers</h2>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3">7.1 Service Disclaimer</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                The Service is provided &quot;as is&quot; and &quot;as available&quot; without any warranties of any kind, either express or implied, including but not limited to warranties of merchantability, fitness for a particular purpose, or non-infringement.
              </p>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">7.2 Content Disclaimer</h3>
              <p className="text-gray-700 leading-relaxed">
                We do not warrant that the certificates created using our Service will be accepted by any institution, organization, or authority. The acceptance and validity of certificates depend on the policies of the receiving party.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">8. Limitation of Liability</h2>
              <p className="text-gray-700 leading-relaxed">
                To the fullest extent permitted by law, we shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data, use, goodwill, or other intangible losses resulting from your use of the Service.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">9. Indemnification</h2>
              <p className="text-gray-700 leading-relaxed">
                You agree to defend, indemnify, and hold us harmless from and against any claims, damages, obligations, losses, liabilities, costs, or debt, and expenses (including attorney&apos;s fees) arising from your use of the Service or violation of these Terms.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">10. Termination</h2>
              <p className="text-gray-700 leading-relaxed">
                We may terminate or suspend your access to the Service immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms. Upon termination, your right to use the Service will cease immediately.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">11. Changes to Terms</h2>
              <p className="text-gray-700 leading-relaxed">
                We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect. Your continued use of the Service after such changes constitutes acceptance of the new Terms.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">12. Governing Law</h2>
              <p className="text-gray-700 leading-relaxed">
                These Terms shall be interpreted and governed by the laws of the jurisdiction in which our company is registered, without regard to its conflict of law provisions.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">13. Contact Information</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                If you have any questions about these Terms of Service, please contact us:
              </p>
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="text-gray-700">
                  <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a><br />
                  <strong>Website:</strong> <a href="https://certificatemaker.app" className="text-blue-600 hover:underline">https://certificatemaker.app</a>
                </p>
              </div>
            </section>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
