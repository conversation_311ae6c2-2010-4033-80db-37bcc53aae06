export default function DemoLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <title>Demo - Certificate Categories Added</title>
        <meta name="description" content="Demonstration of added certificate categories" />
        <style>{`
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 0; 
            padding: 0; 
          }
          .container { max-width: 1200px; margin: 0 auto; }
        `}</style>
      </head>
      <body>
        {children}
      </body>
    </html>
  );
}
