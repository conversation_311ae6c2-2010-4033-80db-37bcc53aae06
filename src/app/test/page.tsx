export default function TestPage() {
  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold text-center mb-8">
          Test Page - UI Optimization
        </h1>
        
        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <section className="py-12 sm:py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-lg mb-8">
            <div className="text-center px-6">
              <div className="flex justify-center mb-6">
                <span className="px-4 py-2 text-sm font-medium bg-gray-100 rounded-full">
                  #1 Free Certificate Maker
                </span>
              </div>

              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Free Online Certificate Maker
              </h2>

              <p className="text-lg sm:text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                Design and generate professional certificates online for free with our certificate maker.
                Choose from premium certificate templates, customize with your details, and download high-quality PDF certificates instantly.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-10">
                <button className="w-full sm:w-auto text-lg px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                  Create Certificate Now
                </button>

                <button className="w-full sm:w-auto text-lg px-8 py-4 border-2 border-gray-300 hover:bg-gray-50 rounded-lg">
                  Browse Templates
                </button>
              </div>

              {/* Trust indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
                <div className="flex flex-col items-center p-3 bg-white rounded-lg shadow-sm">
                  <div className="w-6 h-6 mb-2 bg-green-500 rounded-full"></div>
                  <span className="font-semibold text-gray-900 text-sm">100% Free</span>
                  <span className="text-xs text-gray-600">No hidden costs</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-white rounded-lg shadow-sm">
                  <div className="w-6 h-6 mb-2 bg-blue-500 rounded-full"></div>
                  <span className="font-semibold text-gray-900 text-sm">Instant Download</span>
                  <span className="text-xs text-gray-600">Ready in minutes</span>
                </div>
                <div className="flex flex-col items-center p-3 bg-white rounded-lg shadow-sm">
                  <div className="w-6 h-6 mb-2 bg-purple-500 rounded-full"></div>
                  <span className="font-semibold text-gray-900 text-sm">No Registration</span>
                  <span className="text-xs text-gray-600">Start immediately</span>
                </div>
              </div>
            </div>
          </section>

          {/* Features Section */}
          <section className="py-12 sm:py-16 bg-white">
            <div className="text-center mb-12">
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Why Choose Our Free Certificate Maker?
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Create professional certificates with ease using our powerful online certificate generator and template library
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-200">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <div className="w-6 h-6 bg-blue-600 rounded"></div>
                </div>
                <h3 className="text-lg font-bold mb-2">Professional Templates</h3>
                <p className="text-gray-600 text-sm">
                  Choose from our collection of expertly designed certificate templates for achievements,
                  course completions, event participation, and excellence awards.
                </p>
              </div>

              <div className="text-center p-6 bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-200">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <div className="w-6 h-6 bg-green-600 rounded"></div>
                </div>
                <h3 className="text-lg font-bold mb-2">Instant Generation</h3>
                <p className="text-gray-600 text-sm">
                  Generate your certificates in seconds. Simply fill in the details, customize the design,
                  and download your high-quality PDF certificate instantly.
                </p>
              </div>

              <div className="text-center p-6 bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-200">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <div className="w-6 h-6 bg-purple-600 rounded"></div>
                </div>
                <h3 className="text-lg font-bold mb-2">High-Quality PDFs</h3>
                <p className="text-gray-600 text-sm">
                  Download print-ready PDF certificates with crisp text and professional formatting.
                  Perfect for printing or digital sharing.
                </p>
              </div>
            </div>
          </section>

          <div className="text-center py-8">
            <p className="text-gray-600">
              This is a test page showing the optimized UI design based on Bannerbear's style.
              The layout is more compact with smaller fonts and better spacing.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
