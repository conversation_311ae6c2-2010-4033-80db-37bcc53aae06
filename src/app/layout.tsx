import type { Metadata } from 'next';
import { Inter, Dancing_Script, Playfair_Display, Crimson_Text, Source_Sans_3, Great_Vibes } from 'next/font/google';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import Analytics from '@/components/common/Analytics';
import PerformanceMonitor from '@/components/common/PerformanceMonitor';
import ErrorBoundary from '@/components/common/ErrorBoundary';

// 优化字体加载 - 使用next/font/google
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const dancingScript = Dancing_Script({
  subsets: ['latin'],
  variable: '--font-dancing-script',
  display: 'swap',
});

const playfairDisplay = Playfair_Display({
  subsets: ['latin'],
  weight: ['400', '600', '700'],
  variable: '--font-playfair-display',
  display: 'swap',
});

const crimsonText = Crimson_Text({
  subsets: ['latin'],
  weight: ['400', '600', '700'],
  variable: '--font-crimson-text',
  display: 'swap',
});

const sourceSansPro = Source_Sans_3({
  subsets: ['latin'],
  weight: ['400', '600', '700'],
  variable: '--font-source-sans-pro',
  display: 'swap',
});

const greatVibes = Great_Vibes({
  subsets: ['latin'],
  weight: ['400'],
  variable: '--font-great-vibes',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'Free Certificate Maker | Create Professional PDF Certificates Online',
    template: '%s | Certificate Maker'
  },
  description: 'Create beautiful, professional certificates online for free. Choose from 4 elegant templates, customize with your details, and download high-quality PDF certificates instantly. No registration required.',

  authors: [{ name: 'Certificate Maker Team' }],
  creator: 'Certificate Maker',
  publisher: 'Certificate Maker',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://certificatemaker.com'),
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://certificatemaker.com',
    siteName: 'Certificate Maker',
    title: 'Free Certificate Maker - Create Professional Certificates Online',
    description: 'Generate beautiful PDF certificates instantly with our free online tool. Choose from 4 professional templates and customize with your details.',
    images: [
      {
        url: '/images/og-certificate-maker.jpg',
        width: 1200,
        height: 630,
        alt: 'Certificate Maker - Free Online Certificate Generator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Certificate Maker - Create Professional Certificates',
    description: 'Generate beautiful PDF certificates instantly with our free online tool.',
    images: ['/images/twitter-certificate-maker.jpg'],
  },
  verification: {
    google: 'your-google-verification-code',
  },
  category: 'productivity',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-QQGBB07T2Q"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-QQGBB07T2Q');
            `,
          }}
        />

        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#1E40AF" />
        <meta name="msapplication-TileColor" content="#1E40AF" />
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'WebApplication',
              name: 'Certificate Maker',
              description: 'Free online certificate generator for creating professional PDF certificates',
              url: 'https://certificatemaker.com',
              applicationCategory: 'DesignApplication',
              operatingSystem: 'Web Browser',
              offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD'
              },
              featureList: [
                '4 Professional Certificate Templates',
                'Real-time Preview',
                'High-Quality PDF Generation',
                'Mobile-Friendly Design',
                'No Registration Required'
              ],
              author: {
                '@type': 'Organization',
                name: 'Certificate Maker Team'
              }
            })
          }}
        />
      </head>
      <body className={`${inter.className} ${dancingScript.variable} ${playfairDisplay.variable} ${crimsonText.variable} ${sourceSansPro.variable} ${greatVibes.variable} antialiased`}>
        <ErrorBoundary>
          <div className="min-h-screen bg-white">
            {children}
            <Toaster />
            <Analytics />
            <PerformanceMonitor />
          </div>
        </ErrorBoundary>
      </body>
    </html>
  );
}
