import { NextRequest, NextResponse } from 'next/server';
import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { TemplateManager } from '@/lib/template-manager';
import { PDFTemplateGenerator } from '@/lib/pdf-template-generator';
import { TemplateAdapter, TemplateFactory } from '@/lib/template-adapter';

/**
 * 新的组件化PDF生成API
 * 使用模块化的PDF模板生成器
 */

interface GeneratePDFRequest {
  templateId: string;
  data: CertificateData;
}

export async function POST(request: NextRequest) {
  try {
    const body: GeneratePDFRequest = await request.json();
    
    console.log('📥 Received PDF generation request (v2):', {
      templateId: body.templateId,
      recipientName: body.data.recipientName
    });
    
    // 获取模板
    const template = TemplateManager.getTemplateById(body.templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // 使用新的组件化PDF生成器
    const pdfConfig = TemplateFactory.getPDFConfig(template);
    const pdfData = TemplateAdapter.convertToPDFData(body.data);
    
    console.log('🔧 Using PDF config:', {
      templateId: pdfConfig.id,
      orientation: pdfConfig.orientation,
      fieldsCount: pdfConfig.fields.length
    });
    
    const generator = new PDFTemplateGenerator(pdfConfig, pdfData);
    const pdfBytes = await generator.generate();

    console.log('✅ PDF generated successfully (v2), size:', pdfBytes.length, 'bytes');

    // 返回PDF文件
    return new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${body.data.recipientName || 'certificate'}.pdf"`,
        'Content-Length': pdfBytes.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('❌ PDF generation error (v2):', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate PDF',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * 获取模板配置信息
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('templateId');
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }
    
    // 获取模板
    const template = TemplateManager.getTemplateById(templateId);
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }
    
    // 获取PDF配置
    const pdfConfig = TemplateFactory.getPDFConfig(template);
    
    return NextResponse.json({
      templateId: pdfConfig.id,
      name: pdfConfig.name,
      orientation: pdfConfig.orientation,
      dimensions: pdfConfig.dimensions,
      fieldsCount: pdfConfig.fields.length,
      fields: pdfConfig.fields.map(field => ({
        id: field.id,
        type: field.type,
        position: field.position,
        dimensions: field.dimensions
      }))
    });
    
  } catch (error) {
    console.error('❌ Error getting template config:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get template configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
