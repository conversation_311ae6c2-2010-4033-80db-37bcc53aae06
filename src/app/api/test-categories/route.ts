import { NextResponse } from 'next/server';
import { TemplateManager } from '@/lib/template-manager';

export async function GET() {
  try {
    const categories = TemplateManager.getEnabledCategories();
    
    return NextResponse.json({
      success: true,
      categories: categories.map(category => ({
        id: category.id,
        displayName: category.displayName,
        description: category.description,
        templateCount: category.templateCount,
        enabled: category.enabled
      }))
    });
  } catch (error) {
    console.error('Error loading categories:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
