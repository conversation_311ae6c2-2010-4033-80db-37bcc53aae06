@tailwind base;
@tailwind components;
@tailwind utilities;

/* 字体优化 - 现在使用next/font/google加载 */
/* 移除了Google Fonts的CSS导入，改用next/font优化加载 */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 移动端优化 */
@layer base {
  /* 防止iOS缩放 */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  textarea {
    font-size: 16px;
  }

  /* 触摸优化 */
  button,
  [role="button"],
  input[type="submit"],
  input[type="button"] {
    touch-action: manipulation;
  }

  /* 滚动优化 */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* 证书预览样式 */
@layer components {
  .certificate-preview {
    @apply bg-white shadow-lg rounded-lg overflow-hidden;
    aspect-ratio: 3/4;
  }

  .certificate-template {
    @apply w-full h-full relative;
  }

  .certificate-field {
    @apply absolute text-center;
  }

  /* 字符计数器样式 */
  .character-counter {
    @apply text-xs text-muted-foreground;
  }

  .character-counter.warning {
    @apply text-yellow-600;
  }

  .character-counter.error {
    @apply text-red-600;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }

  /* 移动端优化的表单 */
  .mobile-form-input {
    @apply w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  /* 触摸友好的按钮 */
  .touch-button {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* 紧凑设计优化 */
  .compact-section {
    @apply py-12 sm:py-16;
  }

  .compact-header {
    @apply mb-8 sm:mb-12;
  }

  .compact-title {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold mb-4;
  }

  .compact-subtitle {
    @apply text-lg text-gray-600 max-w-3xl mx-auto;
  }

  .compact-card {
    @apply p-4 sm:p-6 hover:shadow-lg transition-shadow duration-200;
  }

  .compact-icon {
    @apply w-10 h-10 sm:w-12 sm:h-12;
  }

  .compact-button {
    @apply text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 h-auto min-h-[48px] sm:min-h-[52px];
  }
}

/* 性能优化 */
@layer utilities {
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }

  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border: 0 0% 0%;
    --input: 0 0% 0%;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
