'use client';

import Link from 'next/link';
import { TemplateManager } from '@/lib/template-manager';

export default function CategoriesTestPage() {
  // 获取启用的分类数据
  let categories: any[] = [];
  try {
    categories = TemplateManager.getEnabledCategories();
  } catch (error) {
    console.error('Error loading categories:', error);
    categories = [];
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Categories Test Page
          </h1>
          <p className="text-lg text-gray-600">
            Testing the enabled certificate categories and template counts
          </p>
        </div>

        {/* API Test Results */}
        <div className="mb-12 p-6 bg-gray-50 rounded-lg">
          <h2 className="text-2xl font-bold mb-4">API Test Results</h2>
          <p className="mb-4">
            <strong>Total Categories Found:</strong> {categories.length}
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {categories.map((category, index) => (
              <div key={category.id} className="p-4 bg-white rounded border">
                <h3 className="font-bold text-lg">{index + 1}. {category.displayName}</h3>
                <p className="text-sm text-gray-600 mb-2">{category.description}</p>
                <div className="flex justify-between items-center">
                  <span className="px-2 py-1 bg-gray-100 rounded text-sm">
                    {category.templateCount} Templates
                  </span>
                  <span className={`px-2 py-1 rounded text-sm ${category.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {category.enabled ? "Enabled" : "Disabled"}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Templates Preview Section (Optimized Design) */}
        <section className="py-12 sm:py-16 bg-white">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Free Certificate Templates
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose from our professionally designed certificate templates for every occasion and achievement type
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.length > 0 && categories.slice(0, 4).map((category) => (
              <Link key={category.id} href={`/certificate-templates/${category.urlSlug}/`}>
                <div className="p-6 bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-200 cursor-pointer border">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                      <div className="w-6 h-6 bg-white rounded"></div>
                    </div>
                    <h3 className="text-base font-bold mb-2">{category.displayName}</h3>
                    <p className="text-center text-sm text-gray-600 mb-3">
                      {category.description}
                    </p>
                    <div className="text-center">
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs">
                        {category.templateCount} Templates
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
            {categories.length === 0 && (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-600">Templates are being prepared. Please check back soon!</p>
              </div>
            )}
          </div>

          <div className="text-center mt-10">
            <Link href="/certificate-templates/">
              <button className="text-lg px-8 py-4 h-auto min-h-[52px] border-2 border-gray-300 hover:bg-gray-50 rounded-lg">
                📄 View All Templates
              </button>
            </Link>
          </div>
        </section>

        {/* Summary */}
        <div className="mt-12 p-6 bg-blue-50 rounded-lg">
          <h2 className="text-2xl font-bold mb-4 text-blue-900">Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-blue-800">Categories Status:</h3>
              <ul className="list-disc list-inside text-blue-700">
                <li>COMPLETION: 5 templates ✅</li>
                <li>ACHIEVEMENT: 6 templates ✅</li>
                <li>PARTICIPATION: 6 templates ✅</li>
                <li>EXCELLENCE: 5 templates ✅</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-blue-800">Total:</h3>
              <p className="text-blue-700">
                <strong>{categories.length} categories</strong> with{' '}
                <strong>{categories.reduce((sum, cat) => sum + cat.templateCount, 0)} templates</strong> total
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
