import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { PDFTemplateConfig, PDFGenerationData } from './pdf-template-generator';

/**
 * 模板适配器 - 将现有的CertificateTemplate转换为新的PDFTemplateConfig格式
 */
export class TemplateAdapter {
  /**
   * 将CertificateTemplate转换为PDFTemplateConfig
   */
  static convertToPDFConfig(template: CertificateTemplate): PDFTemplateConfig {
    const isLandscape = template.orientation === 'landscape';
    
    return {
      id: template.id,
      name: template.displayName,
      orientation: template.orientation,
      dimensions: {
        width: isLandscape ? 842 : 595,  // A4 dimensions
        height: isLandscape ? 595 : 842
      },
      background: {
        color: template.style?.background || '#ffffff'
      },
      border: {
        enabled: !!template.style?.border,
        color: template.style?.colors?.border || '#2563eb',
        width: 3,
        style: 'solid'
      },
      fields: this.generateFieldsFromTemplate(template)
    };
  }

  /**
   * 将CertificateData转换为PDFGenerationData
   */
  static convertToPDFData(data: CertificateData): PDFGenerationData {
    return {
      name: data.recipientName,
      details: data.details,
      date: data.date,
      signature: data.signature
    };
  }

  /**
   * 根据模板生成字段配置
   */
  private static generateFieldsFromTemplate(template: CertificateTemplate) {
    const isLandscape = template.orientation === 'landscape';
    const width = isLandscape ? 842 : 595;
    const height = isLandscape ? 595 : 842;

    // 基于模板类型和方向生成字段配置
    if (isLandscape) {
      return this.generateLandscapeFields(template, width, height);
    } else {
      return this.generatePortraitFields(template, width, height);
    }
  }

  /**
   * 生成横向模板字段
   */
  private static generateLandscapeFields(template: CertificateTemplate, width: number, height: number) {
    return [
      {
        id: 'name',
        type: 'text' as const,
        position: { x: width / 2, y: height * 0.6 },
        dimensions: { width: width * 0.6, height: 50 },
        text: {
          font: { 
            family: template.style?.fonts?.name?.family || 'Dancing Script',
            weight: 700, 
            size: 36 
          },
          color: template.style?.colors?.primary || '#1f2937',
          align: 'center' as const
        }
      },
      {
        id: 'details',
        type: 'text' as const,
        position: { x: width / 2, y: height * 0.45 },
        dimensions: { width: width * 0.8, height: 60 },
        text: {
          font: { 
            family: template.style?.fonts?.body?.family || 'Open Sans', 
            weight: 400, 
            size: 16 
          },
          color: template.style?.colors?.text || '#374151',
          align: 'center' as const
        }
      },
      {
        id: 'date',
        type: 'text' as const,
        position: { x: width * 0.15, y: height * 0.15 },
        dimensions: { width: width * 0.25, height: 30 },
        text: {
          font: { 
            family: template.style?.fonts?.body?.family || 'Open Sans', 
            weight: 400, 
            size: 14 
          },
          color: template.style?.colors?.secondary || '#6b7280',
          align: 'left' as const
        }
      },
      {
        id: 'signature',
        type: 'text' as const,
        position: { x: width * 0.6, y: height * 0.15 },
        dimensions: { width: width * 0.25, height: 30 },
        text: {
          font: { 
            family: template.style?.fonts?.signature?.family || 'Dancing Script', 
            weight: 500, 
            size: 18 
          },
          color: template.style?.colors?.secondary || '#6b7280',
          align: 'right' as const
        }
      }
    ];
  }

  /**
   * 生成竖向模板字段
   */
  private static generatePortraitFields(template: CertificateTemplate, width: number, height: number) {
    return [
      {
        id: 'name',
        type: 'text' as const,
        position: { x: width / 2, y: height * 0.65 },
        dimensions: { width: width * 0.8, height: 50 },
        text: {
          font: { 
            family: template.style?.fonts?.name?.family || 'Dancing Script',
            weight: 700, 
            size: 32 
          },
          color: template.style?.colors?.primary || '#1f2937',
          align: 'center' as const
        }
      },
      {
        id: 'details',
        type: 'text' as const,
        position: { x: width / 2, y: height * 0.5 },
        dimensions: { width: width * 0.85, height: 80 },
        text: {
          font: { 
            family: template.style?.fonts?.body?.family || 'Open Sans', 
            weight: 400, 
            size: 14 
          },
          color: template.style?.colors?.text || '#374151',
          align: 'center' as const
        }
      },
      {
        id: 'date',
        type: 'text' as const,
        position: { x: width * 0.15, y: height * 0.2 },
        dimensions: { width: width * 0.3, height: 30 },
        text: {
          font: { 
            family: template.style?.fonts?.body?.family || 'Open Sans', 
            weight: 400, 
            size: 12 
          },
          color: template.style?.colors?.secondary || '#6b7280',
          align: 'left' as const
        }
      },
      {
        id: 'signature',
        type: 'text' as const,
        position: { x: width * 0.55, y: height * 0.2 },
        dimensions: { width: width * 0.3, height: 30 },
        text: {
          font: { 
            family: template.style?.fonts?.signature?.family || 'Dancing Script', 
            weight: 500, 
            size: 16 
          },
          color: template.style?.colors?.secondary || '#6b7280',
          align: 'right' as const
        }
      }
    ];
  }

  /**
   * 根据模板ID获取特定的字段配置
   */
  static getCustomFieldConfig(templateId: string) {
    const customConfigs: Record<string, any> = {
      'achievement-1': {
        namePosition: { x: 421, y: 380 },
        nameFont: { family: 'Dancing Script', weight: 700, size: 40 }
      },
      'completion-1': {
        namePosition: { x: 421, y: 360 },
        nameFont: { family: 'Dancing Script', weight: 600, size: 36 }
      },
      'participation-1': {
        namePosition: { x: 421, y: 350 },
        nameFont: { family: 'Dancing Script', weight: 500, size: 34 }
      },
      'excellence-1': {
        namePosition: { x: 421, y: 370 },
        nameFont: { family: 'Dancing Script', weight: 700, size: 42 }
      }
    };

    return customConfigs[templateId] || {};
  }

  /**
   * 应用自定义配置到字段
   */
  static applyCustomConfig(config: PDFTemplateConfig, templateId: string): PDFTemplateConfig {
    const customConfig = this.getCustomFieldConfig(templateId);
    
    if (Object.keys(customConfig).length === 0) {
      return config;
    }

    // 应用自定义配置
    const updatedFields = config.fields.map(field => {
      if (field.id === 'name' && customConfig.namePosition) {
        return {
          ...field,
          position: customConfig.namePosition,
          text: field.text ? {
            ...field.text,
            font: customConfig.nameFont || field.text.font
          } : field.text
        };
      }
      return field;
    });

    return {
      ...config,
      fields: updatedFields
    };
  }
}

/**
 * 模板工厂 - 创建和管理PDF模板配置
 */
export class TemplateFactory {
  private static templateCache = new Map<string, PDFTemplateConfig>();

  /**
   * 获取或创建PDF模板配置
   */
  static getPDFConfig(template: CertificateTemplate): PDFTemplateConfig {
    const cacheKey = template.id;
    
    if (this.templateCache.has(cacheKey)) {
      return this.templateCache.get(cacheKey)!;
    }

    // 转换为PDF配置
    let config = TemplateAdapter.convertToPDFConfig(template);
    
    // 应用自定义配置
    config = TemplateAdapter.applyCustomConfig(config, template.id);
    
    // 缓存配置
    this.templateCache.set(cacheKey, config);
    
    return config;
  }

  /**
   * 清除缓存
   */
  static clearCache() {
    this.templateCache.clear();
  }

  /**
   * 预热缓存 - 为所有模板创建配置
   */
  static warmupCache(templates: CertificateTemplate[]) {
    templates.forEach(template => {
      this.getPDFConfig(template);
    });
    
    console.log(`✅ Template cache warmed up with ${templates.length} templates`);
  }
}
