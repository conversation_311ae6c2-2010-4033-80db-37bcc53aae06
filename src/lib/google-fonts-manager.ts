/**
 * Google Fonts 管理器
 * 基于 Google Fonts API 的完整字体解决方案
 * 参考: https://fonts.google.com/selection/embed
 */

export interface FontConfig {
  family: string;
  weights: number[];
  subsets: string[];
  display: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
}

export interface FontData {
  family: string;
  weight: number;
  style: 'normal' | 'italic';
  url: string;
  data?: ArrayBuffer;
}

// 项目使用的字体配置
export const PROJECT_FONTS: FontConfig[] = [
  {
    family: 'Dancing Script',
    weights: [400, 500, 600, 700],
    subsets: ['latin', 'latin-ext'],
    display: 'swap'
  },
  {
    family: 'Playfair Display',
    weights: [400, 500, 600, 700, 800, 900],
    subsets: ['latin', 'latin-ext'],
    display: 'swap'
  },
  {
    family: 'Inter',
    weights: [100, 200, 300, 400, 500, 600, 700, 800, 900],
    subsets: ['latin', 'latin-ext'],
    display: 'swap'
  },
  {
    family: 'Crimson Text',
    weights: [400, 600, 700],
    subsets: ['latin', 'latin-ext'],
    display: 'swap'
  },
  {
    family: 'Source Sans 3',
    weights: [200, 300, 400, 600, 700, 900],
    subsets: ['latin', 'latin-ext'],
    display: 'swap'
  },
  {
    family: 'Great Vibes',
    weights: [400],
    subsets: ['latin', 'latin-ext'],
    display: 'swap'
  }
];

/**
 * Google Fonts 管理器类
 */
export class GoogleFontsManager {
  private static instance: GoogleFontsManager;
  private fontCache = new Map<string, FontData>();
  private loadingPromises = new Map<string, Promise<FontData | null>>();

  private constructor() {}

  static getInstance(): GoogleFontsManager {
    if (!GoogleFontsManager.instance) {
      GoogleFontsManager.instance = new GoogleFontsManager();
    }
    return GoogleFontsManager.instance;
  }

  /**
   * 生成 Google Fonts CSS URL
   */
  generateCSSUrl(fonts: FontConfig[]): string {
    const families = fonts.map(font => {
      const family = font.family.replace(/\s+/g, '+');
      const weights = font.weights.join(',');
      return `${family}:wght@${weights}`;
    }).join('&family=');

    return `https://fonts.googleapis.com/css2?family=${families}&display=swap`;
  }

  /**
   * 生成项目字体的CSS URL
   */
  getProjectFontsCSSUrl(): string {
    return this.generateCSSUrl(PROJECT_FONTS);
  }

  /**
   * 获取单个字体的CSS URL
   */
  getSingleFontCSSUrl(family: string, weight: number): string {
    const fontFamily = family.replace(/\s+/g, '+');
    return `https://fonts.googleapis.com/css2?family=${fontFamily}:wght@${weight}&display=swap`;
  }

  /**
   * 从CSS响应中提取字体URL
   */
  private async extractFontUrlFromCSS(cssUrl: string): Promise<string | null> {
    try {
      const response = await fetch(cssUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`CSS fetch failed: ${response.status}`);
      }

      const cssText = await response.text();
      console.log(`📄 CSS received for ${cssUrl}`);

      // 提取字体URL - 匹配 url() 中的内容
      const urlMatch = cssText.match(/url\(([^)]+)\)/);
      if (urlMatch && urlMatch[1]) {
        const fontUrl = urlMatch[1].replace(/['"]/g, '');
        console.log(`🔗 Font URL extracted: ${fontUrl}`);
        return fontUrl;
      }

      console.warn('❌ Could not extract font URL from CSS');
      return null;
    } catch (error) {
      console.error(`❌ Error extracting font URL from CSS:`, error);
      return null;
    }
  }

  /**
   * 下载字体数据
   */
  private async downloadFontData(fontUrl: string): Promise<ArrayBuffer | null> {
    try {
      console.log(`📥 Downloading font: ${fontUrl}`);
      
      const response = await fetch(fontUrl);
      if (!response.ok) {
        throw new Error(`Font download failed: ${response.status}`);
      }

      const fontData = await response.arrayBuffer();
      console.log(`✅ Font downloaded: ${fontData.byteLength} bytes`);
      
      return fontData;
    } catch (error) {
      console.error(`❌ Error downloading font:`, error);
      return null;
    }
  }

  /**
   * 验证字体数据
   */
  private validateFontData(fontData: ArrayBuffer): boolean {
    if (fontData.byteLength < 1000) {
      console.warn('❌ Font data too small');
      return false;
    }

    // 检查字体文件签名
    const view = new DataView(fontData);
    const signature = view.getUint32(0, false);
    
    // 支持的字体格式签名
    const validSignatures = [
      0x00010000, // TTF
      0x4F54544F, // OTF ('OTTO')
      0x774F4646, // WOFF ('wOFF')
      0x774F4632, // WOFF2 ('wOF2')
      0x74727565  // TrueType ('true')
    ];

    const isValid = validSignatures.includes(signature);
    if (!isValid) {
      console.warn(`❌ Invalid font signature: 0x${signature.toString(16)}`);
    }

    return isValid;
  }

  /**
   * 加载字体数据
   */
  async loadFont(family: string, weight: number = 400, style: 'normal' | 'italic' = 'normal'): Promise<FontData | null> {
    const cacheKey = `${family}-${weight}-${style}`;

    // 检查缓存
    if (this.fontCache.has(cacheKey)) {
      console.log(`✅ Font found in cache: ${cacheKey}`);
      return this.fontCache.get(cacheKey)!;
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(cacheKey)) {
      console.log(`⏳ Font loading in progress: ${cacheKey}`);
      return await this.loadingPromises.get(cacheKey)!;
    }

    // 开始加载
    const loadingPromise = this.loadFontInternal(family, weight, style);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const result = await loadingPromise;
      if (result) {
        this.fontCache.set(cacheKey, result);
      }
      return result;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 内部字体加载逻辑
   */
  private async loadFontInternal(family: string, weight: number, style: 'normal' | 'italic'): Promise<FontData | null> {
    try {
      console.log(`🔍 Loading font: ${family} ${weight} ${style}`);

      // 1. 获取CSS URL
      const cssUrl = this.getSingleFontCSSUrl(family, weight);
      
      // 2. 从CSS中提取字体URL
      const fontUrl = await this.extractFontUrlFromCSS(cssUrl);
      if (!fontUrl) {
        return null;
      }

      // 3. 下载字体数据
      const fontData = await this.downloadFontData(fontUrl);
      if (!fontData) {
        return null;
      }

      // 4. 验证字体数据
      if (!this.validateFontData(fontData)) {
        return null;
      }

      // 5. 创建字体数据对象
      const result: FontData = {
        family,
        weight,
        style,
        url: fontUrl,
        data: fontData
      };

      console.log(`✅ Font loaded successfully: ${family} ${weight} ${style}`);
      return result;

    } catch (error) {
      console.error(`❌ Error loading font ${family} ${weight} ${style}:`, error);
      return null;
    }
  }

  /**
   * 预加载项目字体
   */
  async preloadProjectFonts(): Promise<void> {
    console.log('🚀 Preloading project fonts...');
    
    const loadPromises: Promise<void>[] = [];

    for (const fontConfig of PROJECT_FONTS) {
      for (const weight of fontConfig.weights) {
        const promise = this.loadFont(fontConfig.family, weight).then(() => {
          console.log(`✅ Preloaded: ${fontConfig.family} ${weight}`);
        }).catch(error => {
          console.warn(`⚠️ Failed to preload: ${fontConfig.family} ${weight}`, error);
        });
        
        loadPromises.push(promise);
      }
    }

    await Promise.all(loadPromises);
    console.log('🎉 Project fonts preloading completed');
  }

  /**
   * 获取字体数据用于PDF嵌入
   */
  async getFontDataForPDF(family: string, weight: number = 400): Promise<ArrayBuffer | null> {
    const fontData = await this.loadFont(family, weight);
    return fontData?.data || null;
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): { size: number; fonts: string[] } {
    return {
      size: this.fontCache.size,
      fonts: Array.from(this.fontCache.keys())
    };
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.fontCache.clear();
    this.loadingPromises.clear();
    console.log('🧹 Font cache cleared');
  }
}
