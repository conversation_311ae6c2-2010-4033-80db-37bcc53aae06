/**
 * 字体配置和工具函数
 * 统一管理next/font和PDF生成中的字体
 */

// 字体映射配置 - 将CSS字体名称映射到实际字体
export const FONT_MAPPING = {
  'Dancing Script': {
    cssClass: 'font-dancing',
    cssVariable: 'var(--font-dancing-script)',
    fallback: 'cursive',
    pdfFontKey: 'Dancing Script',
  },
  'Playfair Display': {
    cssClass: 'font-playfair',
    cssVariable: 'var(--font-playfair-display)',
    fallback: 'serif',
    pdfFontKey: 'Playfair Display',
  },
  'Inter': {
    cssClass: 'font-inter',
    cssVariable: 'var(--font-inter)',
    fallback: 'sans-serif',
    pdfFontKey: 'Inter',
  },
  'Crimson Text': {
    cssClass: 'font-crimson',
    cssVariable: 'var(--font-crimson-text)',
    fallback: 'serif',
    pdfFontKey: 'Crimson Text',
  },
  'Source Sans Pro': {
    cssClass: 'font-source',
    cssVariable: 'var(--font-source-sans-pro)',
    fallback: 'sans-serif',
    pdfFont<PERSON>ey: 'Source Sans 3',
  },
  'Source Sans 3': {
    cssClass: 'font-source',
    cssVariable: 'var(--font-source-sans-pro)',
    fallback: 'sans-serif',
    pdfFontKey: 'Source Sans 3',
  },
  'Great Vibes': {
    cssClass: 'font-great-vibes',
    cssVariable: 'var(--font-great-vibes)',
    fallback: 'cursive',
    pdfFontKey: 'Great Vibes',
  },
} as const;

// 字体权重映射
export const FONT_WEIGHTS = {
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
} as const;

/**
 * 获取字体的CSS类名
 */
export function getFontCssClass(fontFamily: string): string {
  const mapping = FONT_MAPPING[fontFamily as keyof typeof FONT_MAPPING];
  return mapping?.cssClass || 'font-inter';
}

/**
 * 获取字体的CSS变量
 */
export function getFontCssVariable(fontFamily: string): string {
  const mapping = FONT_MAPPING[fontFamily as keyof typeof FONT_MAPPING];
  return mapping?.cssVariable || 'var(--font-inter)';
}

/**
 * 获取字体的PDF键名
 */
export function getPdfFontKey(fontFamily: string): string {
  const mapping = FONT_MAPPING[fontFamily as keyof typeof FONT_MAPPING];
  return mapping?.pdfFontKey || 'Inter';
}

/**
 * 获取字体的后备字体
 */
export function getFontFallback(fontFamily: string): string {
  const mapping = FONT_MAPPING[fontFamily as keyof typeof FONT_MAPPING];
  return mapping?.fallback || 'sans-serif';
}

/**
 * 生成完整的字体样式对象，用于内联样式
 */
export function getFontStyle(fontFamily: string, fontSize: number, fontWeight?: number | string) {
  const cssVariable = getFontCssVariable(fontFamily);
  const fallback = getFontFallback(fontFamily);
  
  return {
    fontFamily: `${cssVariable}, ${fontFamily}, ${fallback}`,
    fontSize: `${fontSize}px`,
    fontWeight: fontWeight || 400,
  };
}

/**
 * 检查字体是否可用
 */
export function isFontAvailable(fontFamily: string): boolean {
  return fontFamily in FONT_MAPPING;
}

/**
 * 获取所有可用字体列表
 */
export function getAvailableFonts(): string[] {
  return Object.keys(FONT_MAPPING);
}
