import { Metadata } from 'next';

/**
 * SEO工具库
 * 提供动态生成SEO元数据的功能
 */

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
}

const DEFAULT_CONFIG = {
  siteName: 'Certificate Maker',
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://certificatemaker.com',
  defaultImage: '/images/og-certificate-maker.jpg',
  twitterHandle: '@certificatemaker',
  locale: 'en_US',
};

/**
 * 生成页面的完整SEO元数据
 */
export function generateSEOMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [], // Keep for backward compatibility but won't be used
    image = DEFAULT_CONFIG.defaultImage,
    url = DEFAULT_CONFIG.baseUrl,
    type = 'website',
    publishedTime,
    modifiedTime,
    author,
    section,
  } = config;

  const fullTitle = title.includes(DEFAULT_CONFIG.siteName)
    ? title
    : `${title} | ${DEFAULT_CONFIG.siteName}`;

  const fullUrl = url.startsWith('http') ? url : `${DEFAULT_CONFIG.baseUrl}${url}`;
  const fullImageUrl = image.startsWith('http') ? image : `${DEFAULT_CONFIG.baseUrl}${image}`;

  return {
    title: fullTitle,
    description,
    // Note: keywords meta tag is no longer used by Google and other major search engines
    authors: author ? [{ name: author }] : undefined,
    creator: DEFAULT_CONFIG.siteName,
    publisher: DEFAULT_CONFIG.siteName,
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(DEFAULT_CONFIG.baseUrl),
    alternates: {
      canonical: fullUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type,
      locale: DEFAULT_CONFIG.locale,
      url: fullUrl,
      siteName: DEFAULT_CONFIG.siteName,
      title: fullTitle,
      description,
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      publishedTime,
      modifiedTime,
      authors: author ? [author] : undefined,
      section,
    },
    twitter: {
      card: 'summary_large_image',
      site: DEFAULT_CONFIG.twitterHandle,
      creator: DEFAULT_CONFIG.twitterHandle,
      title: fullTitle,
      description,
      images: [fullImageUrl],
    },
    verification: {
      google: process.env.GOOGLE_VERIFICATION_CODE,
      yandex: process.env.YANDEX_VERIFICATION_CODE,
    },
  };
}

/**
 * 生成结构化数据
 */
export function generateStructuredData(type: 'WebApplication' | 'Article' | 'Organization', data: any) {
  const baseStructuredData = {
    '@context': 'https://schema.org',
    '@type': type,
  };

  switch (type) {
    case 'WebApplication':
      return {
        ...baseStructuredData,
        name: data.name || DEFAULT_CONFIG.siteName,
        description: data.description,
        url: data.url || DEFAULT_CONFIG.baseUrl,
        applicationCategory: 'DesignApplication',
        operatingSystem: 'Web Browser',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'USD',
        },
        featureList: data.features || [
          '4 Professional Certificate Templates',
          'Real-time Preview',
          'High-Quality PDF Generation',
          'Mobile-Friendly Design',
          'No Registration Required',
        ],
        author: {
          '@type': 'Organization',
          name: data.author || DEFAULT_CONFIG.siteName,
        },
        aggregateRating: data.rating ? {
          '@type': 'AggregateRating',
          ratingValue: data.rating.value,
          ratingCount: data.rating.count,
        } : undefined,
      };

    case 'Organization':
      return {
        ...baseStructuredData,
        name: data.name || DEFAULT_CONFIG.siteName,
        url: data.url || DEFAULT_CONFIG.baseUrl,
        logo: data.logo || `${DEFAULT_CONFIG.baseUrl}/images/logo.png`,
        description: data.description,
        contactPoint: data.contactPoint ? {
          '@type': 'ContactPoint',
          telephone: data.contactPoint.telephone,
          contactType: data.contactPoint.type || 'customer service',
          email: data.contactPoint.email,
        } : undefined,
        sameAs: data.socialMedia || [],
      };

    case 'Article':
      return {
        ...baseStructuredData,
        headline: data.title,
        description: data.description,
        image: data.image,
        author: {
          '@type': 'Person',
          name: data.author,
        },
        publisher: {
          '@type': 'Organization',
          name: DEFAULT_CONFIG.siteName,
          logo: {
            '@type': 'ImageObject',
            url: `${DEFAULT_CONFIG.baseUrl}/images/logo.png`,
          },
        },
        datePublished: data.publishedTime,
        dateModified: data.modifiedTime || data.publishedTime,
        mainEntityOfPage: {
          '@type': 'WebPage',
          '@id': data.url,
        },
      };

    default:
      return baseStructuredData;
  }
}

/**
 * 生成面包屑导航结构化数据
 */
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url.startsWith('http') ? crumb.url : `${DEFAULT_CONFIG.baseUrl}${crumb.url}`,
    })),
  };
}

/**
 * 生成FAQ结构化数据
 */
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

/**
 * 获取页面的规范URL
 */
export function getCanonicalUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${DEFAULT_CONFIG.baseUrl}${cleanPath}`;
}

/**
 * 生成页面标题
 */
export function generatePageTitle(title: string, includesSiteName = false): string {
  if (includesSiteName || title.includes(DEFAULT_CONFIG.siteName)) {
    return title;
  }
  return `${title} | ${DEFAULT_CONFIG.siteName}`;
}
