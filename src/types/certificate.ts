/**
 * 证书模板类型定义
 */

export interface FixedPosition {
  x: number;
  y: number;
  width: number;
  height: number;
  align: 'left' | 'center' | 'right';
  fontSize: number;
  fontFamily: string;
  color: string;
  fontWeight?: number;
}

export interface FontConfig {
  family: string;
  size: number;
  weight: number;
  color: string;
}

export interface ColorScheme {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  border?: string;
}

export interface CertificateTemplate {
  // 基础信息
  id: string;
  name: string;
  displayName: string;
  description: string;

  // 分类信息
  category: CertificateCategory;
  tags: string[];

  // 视觉属性
  preview: string;
  backgroundImage?: string; // 可选的背景图片路径
  orientation: 'portrait' | 'landscape';
  aspectRatio: number; // 宽高比，用于预览和PDF生成

  // SEO属性
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  style: {
    background: string;
    border: string;
    colors: ColorScheme;
    fonts: {
      name: FontConfig;
      body: FontConfig;
      signature: FontConfig;
    };
  };
  layout: {
    name: FixedPosition;
    date: FixedPosition;
    signature: FixedPosition;
    details: FixedPosition;
  };
  constraints: {
    nameMaxLength: number;
    nameMinLength: number;
    dateMaxLength: number;
    dateMinLength: number;
    signatureMaxLength: number;
    signatureMinLength: number;
    detailsMaxLength: number;
    detailsMinLength: number;
  };
  validation: {
    namePattern: RegExp;
    datePattern: RegExp;
    signaturePattern: RegExp;
    detailsPattern: RegExp;
  };
}

export interface CertificateData {
  templateId: string;
  recipientName: string;
  date: string;
  signature: string;
  details: string;
}

export interface ValidationResult {
  isValid: boolean;
  message?: string;
  field?: keyof CertificateData;
}

export interface FormFieldConfig {
  name: keyof CertificateData;
  label: string;
  placeholder: string;
  type: 'text' | 'date' | 'textarea';
  maxLength: number;
  minLength: number;
  required: boolean;
  validation: RegExp;
  errorMessage: string;
  mobileKeyboard?: 'default' | 'numeric' | 'email' | 'tel';
}

export interface CertificateGenerationOptions {
  template: CertificateTemplate;
  data: CertificateData;
  quality: 'standard' | 'high';
  format: 'pdf';
}

export interface GenerationProgress {
  stage: 'preparing' | 'rendering' | 'generating' | 'complete' | 'error';
  progress: number;
  message: string;
}

export interface CertificatePreview {
  templateId: string;
  previewUrl: string;
  timestamp: number;
}

// 证书分类枚举
export enum CertificateCategory {
  ACHIEVEMENT = 'achievement',           // 成就证书
  COMPLETION = 'completion',            // 完成证书
  PARTICIPATION = 'participation',      // 参与证书
  EXCELLENCE = 'excellence',            // 优秀证书
  CUSTOM = 'custom'                     // 自定义证书
}

// 模板分类配置接口
export interface TemplateCategory {
  id: CertificateCategory;
  name: string;
  displayName: string;
  description: string;
  seoKeywords: string[];
  urlSlug: string;
  metaTitle: string;
  metaDescription: string;
  templates: CertificateTemplate[];
  defaultSize: 'portrait' | 'landscape';
  defaultTemplate: string;
  // 新增字段
  previewImage?: string; // 分类预览图片
  templateCount?: number; // 模板数量
  enabled?: boolean; // 是否启用该分类，默认为true
}





// 错误类型
export interface CertificateError {
  code: string;
  message: string;
  field?: keyof CertificateData;
  details?: any;
}

// 分析事件类型
export interface AnalyticsEvent {
  event: string;
  category: string;
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
}

// 用户偏好设置
export interface UserPreferences {
  preferredTemplate?: string;
  autoSave: boolean;
  showPreview: boolean;
  mobileOptimized: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// 本地存储数据结构
export interface LocalStorageData {
  formData?: Partial<CertificateData>;
  selectedTemplate?: string;
  preferences?: UserPreferences;
  lastUsed?: number;
}

// API响应类型
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: CertificateError;
  timestamp: number;
}

// 性能指标
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  generationTime: number;
  downloadTime: number;
  totalTime: number;
}

// 设备信息
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
  userAgent: string;
  touchSupport: boolean;
}
