'use client';

import React from 'react';
import { CertificateData, CertificateTemplate } from '@/types/certificate';
import { getFontStyle } from '@/lib/fonts';

interface CertificatePreviewProps {
  template: CertificateTemplate;
  formData: CertificateData;
  showCoordinates?: boolean;
}



export default function CertificatePreview({
  template,
  formData,
  showCoordinates = false,
}: CertificatePreviewProps) {
  // 根据模板方向确定预览容器的样式
  const previewContainerClass = template.orientation === 'landscape'
    ? 'aspect-[4/3] w-full max-w-2xl mx-auto' // 横向模板预览
    : 'aspect-[3/4] w-full max-w-md mx-auto';  // 竖向模板预览

  // 计算精确的缩放比例和容器尺寸
  const getScaleFactorAndDimensions = () => {
    if (template.orientation === 'landscape') {
      // 横向模板：标准PDF尺寸 842x595 点 (A4横向)
      const pdfWidth = 842;
      const pdfHeight = 595;
      // 基于容器的实际显示尺寸计算缩放比例
      // 假设容器最大宽度为 800px (max-w-2xl)
      const containerDisplayWidth = 800;
      const scaleFactor = containerDisplayWidth / pdfWidth;

      return {
        scaleFactor,
        containerWidth: pdfWidth,
        containerHeight: pdfHeight,
        displayWidth: containerDisplayWidth,
        displayHeight: pdfHeight * scaleFactor
      };
    } else {
      // 竖向模板：标准PDF尺寸 595x842 点 (A4竖向)
      const pdfWidth = 595;
      const pdfHeight = 842;
      // 基于容器的实际显示尺寸计算缩放比例
      // 假设容器最大宽度为 448px (max-w-md)
      const containerDisplayWidth = 448;
      const scaleFactor = containerDisplayWidth / pdfWidth;

      return {
        scaleFactor,
        containerWidth: pdfWidth,
        containerHeight: pdfHeight,
        displayWidth: containerDisplayWidth,
        displayHeight: pdfHeight * scaleFactor
      };
    }
  };

  const { scaleFactor, containerWidth, containerHeight, displayWidth, displayHeight } = getScaleFactorAndDimensions();

  return (
    <div className="w-full">
      {/* <h3 className="text-lg font-semibold mb-4 text-gray-900">Preview</h3> */}
      <div className={`${previewContainerClass} relative`}>
        <div
          className="relative h-full w-full rounded-lg shadow-lg overflow-hidden"
          style={{
            backgroundColor: template.style.colors.background,
            fontFamily: template.style.fonts.body.family,
          }}
        >
          {/* 背景图片支持 */}
          {template.backgroundImage && (
            <div className="absolute inset-0">
              <img
                src={template.backgroundImage}
                alt="Certificate background"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {/* 边框装饰 */}
          {/* <div
            className="absolute inset-2 rounded border-2"
            style={{
              borderColor: template.style.colors.primary,
              borderWidth: template.style.border.includes('3pt') ? '3px' :
                          template.style.border.includes('2pt') ? '2px' : '1px',
            }}
          /> */}

          {/* 证书内容 - 使用绝对定位匹配PDF坐标 */}
          <div className="relative h-full w-full">

            {/* 收件人姓名 - 使用精确坐标定位，匹配PDF坐标系统 */}
            {formData.recipientName && (
              <div
                className="absolute"
                style={{
                  left: `${template.layout.name.x * scaleFactor}px`,
                  top: `${(containerHeight - template.layout.name.y - template.layout.name.height) * scaleFactor}px`,
                  width: `${template.layout.name.width * scaleFactor}px`,
                  height: `${template.layout.name.height * scaleFactor}px`,
                  textAlign: template.layout.name.align,
                  color: template.layout.name.color,
                  ...getFontStyle(
                    template.layout.name.fontFamily,
                    template.layout.name.fontSize * scaleFactor,
                    template.layout.name.fontWeight
                  ),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: template.layout.name.align === 'center' ? 'center' :
                                 template.layout.name.align === 'right' ? 'flex-end' : 'flex-start',
                  lineHeight: '1.0',
                  letterSpacing: template.layout.name.fontFamily.includes('Dancing') ? '0.02em' : 'normal',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.recipientName}
              </div>
            )}

            {/* 详细信息 - 使用精确坐标定位，匹配PDF坐标系统 */}
            {formData.details && (
              <div
                className="absolute"
                style={{
                  left: `${template.layout.details.x * scaleFactor}px`,
                  top: `${(containerHeight - template.layout.details.y - template.layout.details.height) * scaleFactor}px`,
                  width: `${template.layout.details.width * scaleFactor}px`,
                  height: `${template.layout.details.height * scaleFactor}px`,
                  textAlign: template.layout.details.align,
                  color: template.layout.details.color,
                  ...getFontStyle(
                    template.layout.details.fontFamily,
                    template.layout.details.fontSize * scaleFactor,
                    template.layout.details.fontWeight
                  ),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: template.layout.details.align === 'center' ? 'center' :
                                 template.layout.details.align === 'right' ? 'flex-end' : 'flex-start',
                  lineHeight: '1.3',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.details}
              </div>
            )}

            {/* 日期 - 使用精确坐标定位，匹配PDF坐标系统 */}
            {formData.date && (
              <div
                className="absolute"
                style={{
                  left: `${template.layout.date.x * scaleFactor}px`,
                  top: `${(containerHeight - template.layout.date.y - template.layout.date.height) * scaleFactor}px`,
                  width: `${template.layout.date.width * scaleFactor}px`,
                  height: `${template.layout.date.height * scaleFactor}px`,
                  textAlign: template.layout.date.align,
                  color: template.layout.date.color,
                  ...getFontStyle(
                    template.layout.date.fontFamily,
                    template.layout.date.fontSize * scaleFactor,
                    template.layout.date.fontWeight
                  ),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: template.layout.date.align === 'center' ? 'center' :
                                 template.layout.date.align === 'right' ? 'flex-end' : 'flex-start',
                  lineHeight: '1.0',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.date}
              </div>
            )}

            {/* 签名 - 使用精确坐标定位，匹配PDF坐标系统 */}
            {formData.signature && (
              <div
                className="absolute"
                style={{
                  left: `${template.layout.signature.x * scaleFactor}px`,
                  top: `${(containerHeight - template.layout.signature.y - template.layout.signature.height) * scaleFactor}px`,
                  width: `${template.layout.signature.width * scaleFactor}px`,
                  height: `${template.layout.signature.height * scaleFactor}px`,
                  textAlign: template.layout.signature.align,
                  color: template.layout.signature.color,
                  ...getFontStyle(
                    template.layout.signature.fontFamily,
                    template.layout.signature.fontSize * scaleFactor,
                    template.layout.signature.fontWeight
                  ),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: template.layout.signature.align === 'center' ? 'center' :
                                 template.layout.signature.align === 'right' ? 'flex-end' : 'flex-start',
                  lineHeight: '1.0',
                  letterSpacing: template.layout.signature.fontFamily.includes('Dancing') ||
                                template.layout.signature.fontFamily.includes('Great Vibes') ? '0.02em' : 'normal',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.signature}
              </div>
            )}

            {/* 坐标调试信息 */}
            {showCoordinates && (
              <>
                {/* 姓名区域边框 */}
                <div
                  className="absolute border-2 border-red-500 bg-red-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.name.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.name.y - template.layout.name.height) * scaleFactor}px`,
                    width: `${template.layout.name.width * scaleFactor}px`,
                    height: `${template.layout.name.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-red-600 bg-white px-1">姓名</div>
                </div>

                {/* 详情区域边框 */}
                <div
                  className="absolute border-2 border-blue-500 bg-blue-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.details.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.details.y - template.layout.details.height) * scaleFactor}px`,
                    width: `${template.layout.details.width * scaleFactor}px`,
                    height: `${template.layout.details.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-blue-600 bg-white px-1">详情</div>
                </div>

                {/* 日期区域边框 */}
                <div
                  className="absolute border-2 border-green-500 bg-green-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.date.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.date.y - template.layout.date.height) * scaleFactor}px`,
                    width: `${template.layout.date.width * scaleFactor}px`,
                    height: `${template.layout.date.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-green-600 bg-white px-1">日期</div>
                </div>

                {/* 签名区域边框 */}
                <div
                  className="absolute border-2 border-purple-500 bg-purple-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.signature.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.signature.y - template.layout.signature.height) * scaleFactor}px`,
                    width: `${template.layout.signature.width * scaleFactor}px`,
                    height: `${template.layout.signature.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-purple-600 bg-white px-1">签名</div>
                </div>
              </>
            )}

            {/* 坐标调试信息 */}
            {showCoordinates && (
              <>
                {/* 姓名区域边框 */}
                <div
                  className="absolute border-2 border-red-500 bg-red-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.name.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.name.y - template.layout.name.height) * scaleFactor}px`,
                    width: `${template.layout.name.width * scaleFactor}px`,
                    height: `${template.layout.name.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-red-600 bg-white px-1">姓名</div>
                </div>

                {/* 详情区域边框 */}
                <div
                  className="absolute border-2 border-blue-500 bg-blue-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.details.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.details.y - template.layout.details.height) * scaleFactor}px`,
                    width: `${template.layout.details.width * scaleFactor}px`,
                    height: `${template.layout.details.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-blue-600 bg-white px-1">详情</div>
                </div>

                {/* 日期区域边框 */}
                <div
                  className="absolute border-2 border-green-500 bg-green-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.date.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.date.y - template.layout.date.height) * scaleFactor}px`,
                    width: `${template.layout.date.width * scaleFactor}px`,
                    height: `${template.layout.date.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-green-600 bg-white px-1">日期</div>
                </div>

                {/* 签名区域边框 */}
                <div
                  className="absolute border-2 border-purple-500 bg-purple-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.signature.x * scaleFactor}px`,
                    top: `${(containerHeight - template.layout.signature.y - template.layout.signature.height) * scaleFactor}px`,
                    width: `${template.layout.signature.width * scaleFactor}px`,
                    height: `${template.layout.signature.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-purple-600 bg-white px-1">签名</div>
                </div>
              </>
            )}
          </div>

          {/* 装饰元素 */}
          {/* {template.id === 'classic-business' && (
            <>
              <div
                className="absolute top-4 left-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute top-4 right-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute bottom-4 left-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute bottom-4 right-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
            </>
          )} */}

          {template.id === 'elegant-green' && (
            <>
              <div
                className="absolute top-6 left-1/2 transform -translate-x-1/2 w-12 h-1 opacity-20"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
              <div
                className="absolute bottom-6 left-1/2 transform -translate-x-1/2 w-12 h-1 opacity-20"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
            </>
          )}
        </div>
      </div>

      {/* 预览说明和操作 */}

    </div>
  );
}
