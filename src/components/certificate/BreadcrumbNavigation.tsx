import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  name: string;
  url: string;
  current?: boolean;
}

interface BreadcrumbNavigationProps {
  items: BreadcrumbItem[];
}

export default function BreadcrumbNavigation({ items }: BreadcrumbNavigationProps) {
  return (
    <nav aria-label="Breadcrumb" className="mb-8">
      <ol className="flex items-center space-x-2 text-sm text-gray-500">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="w-4 h-4 mx-2 text-gray-400" aria-hidden="true" />
            )}
            
            {item.current ? (
              <span 
                className="font-medium text-gray-900 truncate max-w-[200px]" 
                aria-current="page"
                title={item.name}
              >
                {index === 0 && <Home className="w-4 h-4 mr-1 inline" />}
                {item.name}
              </span>
            ) : (
              <Link
                href={item.url}
                className="hover:text-blue-600 transition-colors truncate max-w-[200px] flex items-center"
                title={item.name}
              >
                {index === 0 && <Home className="w-4 h-4 mr-1" />}
                {item.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
