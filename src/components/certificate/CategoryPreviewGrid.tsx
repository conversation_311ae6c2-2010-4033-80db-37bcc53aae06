'use client';

import { TemplateCategory } from '@/types/certificate';
import CategoryPreviewCard from './CategoryPreviewCard';
import { FadeIn, SlideIn } from '@/components/ui/animations';

interface CategoryPreviewGridProps {
  categories: TemplateCategory[];
  title?: string;
  subtitle?: string;
  className?: string;
}

export default function CategoryPreviewGrid({ 
  categories, 
  title = "Certificate Templates by Category",
  subtitle = "Choose from our professionally designed certificate templates",
  className = '' 
}: CategoryPreviewGridProps) {
  return (
    <div className={`w-full ${className}`}>
      {/* 标题区域 */}
      <div className="text-center mb-8 sm:mb-12 px-4">
        <FadeIn>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 leading-tight">
            {title}
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            {subtitle}
          </p>
        </FadeIn>
      </div>

      {/* 分类网格 - 主要横向展示，一行4个 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 px-4 sm:px-0">
        {categories.map((category, index) => (
          <SlideIn key={category.id} delay={index * 0.1}>
            <CategoryPreviewCard 
              category={category}
              className="h-full"
            />
          </SlideIn>
        ))}
      </div>

      {/* 统计信息 */}
      <div className="mt-8 sm:mt-12 text-center px-4">
        <FadeIn delay={0.3}>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-6 md:space-x-8 text-xs sm:text-sm text-gray-500">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>
                {categories.reduce((total, cat) => total + (cat.templateCount || 0), 0)} Total Templates
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Instant PDF Download</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>100% Customizable</span>
            </div>
          </div>
        </FadeIn>
      </div>
    </div>
  );
}
