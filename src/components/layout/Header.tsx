'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Menu, X } from 'lucide-react';

interface HeaderProps {
  className?: string;
}

// 定义页面类型
type PageType = 'home' | 'templates' | 'category' | 'other';

// 根据路径判断页面类型
function getPageType(pathname: string): PageType {
  if (pathname === '/') return 'home';
  if (pathname === '/certificate-templates' || pathname === '/certificate-templates/') return 'templates';
  if (pathname.startsWith('/certificate-templates/')) return 'category';
  return 'other';
}

export default function Header({ className }: HeaderProps) {
  const pathname = usePathname();
  const pageType = getPageType(pathname);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  return (
    <header className={cn(
      'sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
      className
    )}>
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">CM</span>
          </div>
          <span className="font-bold text-xl">Certificate Maker</span>
        </Link>

        {/* Navigation - 根据页面类型动态显示 */}
        <nav className="hidden md:flex items-center space-x-8">
          {pageType === 'home' && (
            <>
              <Link
                href="/certificate-templates/"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Templates
              </Link>
              <Link
                href="#how-it-works"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                How It Works
              </Link>
              <Link
                href="#templates"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Examples
              </Link>
            </>
          )}

          {(pageType === 'templates' || pageType === 'category') && (
            <>
              <Link
                href="/"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Home
              </Link>
              <Link
                href="/certificate-templates/"
                className={cn(
                  "text-sm font-medium transition-colors hover:text-primary",
                  pageType === 'templates' ? "text-primary" : "text-muted-foreground"
                )}
              >
                All Templates
              </Link>
            </>
          )}

          {pageType === 'other' && (
            <>
              <Link
                href="/"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Home
              </Link>
              <Link
                href="/certificate-templates/"
                className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
              >
                Templates
              </Link>
            </>
          )}
        </nav>

        {/* CTA Button - 根据页面类型调整 */}
        <div className="hidden md:block">
          {pageType === 'home' && (
            <Link
              href="/certificate-templates/"
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              Create Certificate
            </Link>
          )}

          {(pageType === 'templates' || pageType === 'category') && (
            <Link
              href="/certificate-templates/"
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              Browse Templates
            </Link>
          )}

          {pageType === 'other' && (
            <Link
              href="/certificate-templates/"
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              Create Certificate
            </Link>
          )}
        </div>

        {/* Mobile menu button */}
        <button
          className="md:hidden p-2 rounded-md hover:bg-accent transition-colors"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          {isMobileMenuOpen ? (
            <X className="w-5 h-5" />
          ) : (
            <Menu className="w-5 h-5" />
          )}
        </button>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t bg-background/95 backdrop-blur">
          <div className="container mx-auto px-4 py-4 space-y-4">
            {pageType === 'home' && (
              <>
                <Link
                  href="/certificate-templates/"
                  className="block text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Templates
                </Link>
                <Link
                  href="#how-it-works"
                  className="block text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  How It Works
                </Link>
                <Link
                  href="#templates"
                  className="block text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Examples
                </Link>
              </>
            )}

            {(pageType === 'templates' || pageType === 'category') && (
              <>
                <Link
                  href="/"
                  className="block text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Home
                </Link>
                <Link
                  href="/certificate-templates/"
                  className={cn(
                    "block text-sm font-medium transition-colors hover:text-primary",
                    pageType === 'templates' ? "text-primary" : "text-muted-foreground"
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  All Templates
                </Link>
              </>
            )}

            {pageType === 'other' && (
              <>
                <Link
                  href="/"
                  className="block text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Home
                </Link>
                <Link
                  href="/certificate-templates/"
                  className="block text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Templates
                </Link>
              </>
            )}

            {/* Mobile CTA Button */}
            <div className="pt-4 border-t">
              {pageType === 'home' && (
                <Link
                  href="/certificate-templates/"
                  className="block w-full text-center bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Create Certificate
                </Link>
              )}

              {(pageType === 'templates' || pageType === 'category') && (
                <Link
                  href="/certificate-templates/"
                  className="block w-full text-center bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Browse Templates
                </Link>
              )}

              {pageType === 'other' && (
                <Link
                  href="/certificate-templates/"
                  className="block w-full text-center bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Create Certificate
                </Link>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
