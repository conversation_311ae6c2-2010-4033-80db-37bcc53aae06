'use client';

import Link from 'next/link';
import { TemplateManager } from '@/lib/template-manager';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PageLayout from '@/components/layout/PageLayout';

import { Zap, Download, Sparkles, Award, FileText, Users, Star, CheckCircle, Clock, Shield } from 'lucide-react';

export default function HomePage() {
  // 获取启用的分类数据
  const categories = TemplateManager.getEnabledCategories();

  return (
    <PageLayout>
      <div className="min-h-screen">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-16 sm:py-20 lg:py-24">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-6xl mx-auto">
              <div className="flex justify-center mb-8">
                <Badge variant="secondary" className="px-6 py-3 text-base font-medium">
                  <Sparkles className="w-5 h-5 mr-2" />
                  #1 Free Certificate Maker
                </Badge>
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold text-gray-900 mb-8 leading-tight">
                Free Online
                <span className="text-blue-600 block sm:inline sm:ml-4">
                  Certificate Maker
                </span>
                <span className="block text-3xl sm:text-4xl lg:text-5xl text-gray-700 mt-2">
                  Create Professional Certificates Instantly
                </span>
              </h1>

              <p className="text-xl sm:text-2xl text-gray-600 mb-10 max-w-6xl mx-auto leading-relaxed">
                Design and generate professional certificates online for free with our certificate maker.
                Choose from premium certificate templates, customize with your details, and download high-quality PDF certificates instantly.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
                <Link href="/certificate-templates/">
                  <Button size="lg" className="w-full sm:w-auto text-xl px-12 py-6 h-auto min-h-[64px] bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200">
                    <Award className="w-6 h-6 mr-3" />
                    Create Certificate Now
                  </Button>
                </Link>

                <Link href="#templates">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto text-xl px-12 py-6 h-auto min-h-[64px] border-2 hover:bg-gray-50">
                    <FileText className="w-6 h-6 mr-3" />
                    Browse Templates
                  </Button>
                </Link>
              </div>

              {/* Trust indicators */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
                <div className="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm">
                  <CheckCircle className="w-8 h-8 mb-2 text-green-500" />
                  <span className="font-semibold text-gray-900">100% Free</span>
                  <span className="text-sm text-gray-600">No hidden costs</span>
                </div>
                <div className="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm">
                  <Clock className="w-8 h-8 mb-2 text-blue-500" />
                  <span className="font-semibold text-gray-900">Instant Download</span>
                  <span className="text-sm text-gray-600">Ready in minutes</span>
                </div>
                <div className="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm">
                  <Shield className="w-8 h-8 mb-2 text-purple-500" />
                  <span className="font-semibold text-gray-900">No Registration</span>
                  <span className="text-sm text-gray-600">Start immediately</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 sm:py-20 bg-white" aria-labelledby="features-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-16">
              <h2 id="features-heading" className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Why Choose Our Free Certificate Maker?
              </h2>
              <p className="text-xl text-gray-600 max-w-6xl mx-auto">
                Create professional certificates with ease using our powerful online certificate generator and template library
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="text-center p-8 hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl font-bold">Professional Templates</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    Choose from our collection of expertly designed certificate templates for achievements,
                    course completions, event participation, and excellence awards.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-8 hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Zap className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle className="text-xl font-bold">Instant Generation</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    Generate your certificates in seconds. Simply fill in the details, customize the design,
                    and download your high-quality PDF certificate instantly.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-8 hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Download className="w-8 h-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-xl font-bold">High-Quality PDFs</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    Download print-ready PDF certificates with crisp text and professional formatting.
                    Perfect for printing or digital sharing.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-8 hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-yellow-600" />
                  </div>
                  <CardTitle className="text-xl font-bold">Multiple Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    Create certificates for various purposes including academic achievements,
                    course completions, event participation, and excellence recognition.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-8 hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Star className="w-8 h-8 text-red-600" />
                  </div>
                  <CardTitle className="text-xl font-bold">Easy Customization</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    Personalize your certificates with custom names, dates, signatures, and detailed descriptions.
                    No design skills required.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center p-8 hover:shadow-lg transition-shadow duration-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-indigo-600" />
                  </div>
                  <CardTitle className="text-xl font-bold">Completely Free</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    Create unlimited certificates at no cost. No subscriptions, no hidden fees,
                    and no registration required to get started.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>



        {/* How It Works Section */}
        <section id="how-it-works" className="py-16 sm:py-20 bg-gray-50" aria-labelledby="how-it-works-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-16">
              <h2 id="how-it-works-heading" className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                How to Create Your Certificate Online
              </h2>
              <p className="text-xl text-gray-600 max-w-6xl mx-auto">
                Generate professional certificates in just 3 simple steps with our free certificate maker
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
              <div className="text-center">
                <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold text-white">1</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Choose Template</h3>
                <p className="text-gray-600 text-lg">
                  Browse our collection of professional certificate templates.
                  Select the perfect design for your achievement, completion, or participation certificate.
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold text-white">2</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Customize Details</h3>
                <p className="text-gray-600 text-lg">
                  Fill in the recipient name, achievement details, date, and signature.
                  Our smart form guides you through each field with helpful suggestions.
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold text-white">3</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Download PDF</h3>
                <p className="text-gray-600 text-lg">
                  Generate and download your high-quality PDF certificate instantly.
                  Print it or share it digitally - it&apos;s ready to use immediately.
                </p>
              </div>
            </div>

            <div className="text-center mt-12">
              <Link href="/certificate-templates/">
                <Button size="lg" className="text-xl px-12 py-6 h-auto min-h-[64px] bg-blue-600 hover:bg-blue-700">
                  <Award className="w-6 h-6 mr-3" />
                  Start Creating Now
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Templates Preview Section */}
        <section id="templates" className="py-16 sm:py-20 bg-white" aria-labelledby="templates-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-16">
              <h2 id="templates-heading" className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Free Certificate Templates
              </h2>
              <p className="text-xl text-gray-600 max-w-6xl mx-auto">
                Choose from our professionally designed certificate templates for every occasion and achievement type
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.slice(0, 4).map((category) => (
                <Link key={category.id} href={`/certificate-templates/${category.urlSlug}/`}>
                  <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                    <CardHeader className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Award className="w-8 h-8 text-white" />
                      </div>
                      <CardTitle className="text-lg">{category.displayName}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center">
                        {category.description}
                      </CardDescription>
                      <div className="text-center mt-4">
                        <Badge variant="secondary">
                          {category.templateCount} Templates
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>

            <div className="text-center mt-12">
              <Link href="/certificate-templates/">
                <Button variant="outline" size="lg" className="text-xl px-12 py-6 h-auto min-h-[64px] border-2">
                  <FileText className="w-6 h-6 mr-3" />
                  View All Templates
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 sm:py-20 bg-gray-50" aria-labelledby="faq-heading">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-16">
              <h2 id="faq-heading" className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600 max-w-6xl mx-auto">
                Everything you need to know about our free online certificate maker
              </p>
            </header>

            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Is the certificate maker really free?</h3>
                  <p className="text-gray-600">
                    Yes! Our certificate maker is completely free to use. You can create unlimited certificates
                    without any cost, subscription, or hidden fees.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">What formats can I download?</h3>
                  <p className="text-gray-600">
                    All certificates are generated as high-quality PDF files that are perfect for printing
                    or digital sharing. PDFs maintain their quality at any size.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Do I need to register an account?</h3>
                  <p className="text-gray-600">
                    No registration required! You can start creating certificates immediately without
                    providing any personal information or creating an account.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Can I customize the certificate design?</h3>
                  <p className="text-gray-600">
                    You can customize all text fields including recipient name, achievement details,
                    date, and signature. The visual design is professionally optimized for best results.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">Are the certificates print-ready?</h3>
                  <p className="text-gray-600">
                    Absolutely! All certificates are generated in high-resolution PDF format that&apos;s
                    perfect for professional printing on standard paper sizes.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">How long does it take to create a certificate?</h3>
                  <p className="text-gray-600">
                    Creating a certificate takes less than 2 minutes. Simply choose a template,
                    fill in your details, and download your professional certificate instantly.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-16 sm:py-20 bg-blue-600">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
              Ready to Create Your Certificate?
            </h2>
            <p className="text-xl text-blue-100 mb-10 max-w-3xl mx-auto">
              Join thousands of users who trust our certificate maker for their professional needs.
              Start creating beautiful certificates today - it&apos;s completely free!
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link href="/certificate-templates/">
                <Button size="lg" className="w-full sm:w-auto text-xl px-12 py-6 h-auto min-h-[64px] bg-white text-blue-600 hover:bg-gray-100">
                  <Award className="w-6 h-6 mr-3" />
                  Create Your Certificate
                </Button>
              </Link>

            </div>
          </div>
        </section>


      </div>
    </PageLayout>
  );
}
