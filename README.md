# Certificate Maker

一个现代化的证书生成器，基于 Next.js 14、TypeScript 和 Tailwind CSS 构建。支持多种模板、实时预览和高质量PDF生成，特别优化了Dancing Script等手写字体的显示效果。

## 🌟 核心特性

- **多种证书模板**: 提供多种专业设计的证书模板
- **实时预览**: 编辑时即时查看证书效果
- **高质量PDF生成**: 支持客户端和服务器端PDF生成，确保字体正确嵌入
- **智能字体管理**: 统一的字体嵌入系统，支持本地字体和Google Fonts
- **字体缓存优化**: 智能缓存机制，提升字体加载性能
- **优雅降级**: 多层次字体降级策略，确保系统稳定性
- **响应式设计**: 完美适配桌面、平板和移动设备
- **SEO优化**: 内置SEO功能，提升搜索引擎可见性
- **TypeScript**: 全面的类型安全保障
- **现代UI**: 基于Tailwind CSS的清洁直观界面

## 🎯 Dancing Script字体解决方案

本项目特别解决了Dancing Script字体在PDF生成中的显示问题：

- ✅ **完整字体支持**: 包含Regular、Medium、SemiBold、Bold四种权重
- ✅ **统一字体嵌入器**: 整合多种字体加载策略
- ✅ **智能缓存管理**: 减少重复下载，提升性能
- ✅ **多层次降级**: 确保字体始终可用
- ✅ **服务器端生成**: 提供高质量PDF输出选项
- ✅ **全面测试验证**: 包含完整的测试套件

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm、yarn 或 pnpm

### 安装步骤

1. 克隆仓库:
```bash
git clone <repository-url>
cd certificate-maker
```

2. 安装依赖:
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

3. 启动开发服务器:
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

4. 在浏览器中打开 [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
certificate-maker/
├── src/
│   ├── app/                 # Next.js 14 App Router
│   │   ├── api/            # API路由
│   │   ├── tests/          # 测试页面
│   │   └── ...
│   ├── components/          # 可复用React组件
│   ├── lib/                 # 工具函数和配置
│   │   ├── unified-font-embedder.ts      # 统一字体嵌入器
│   │   ├── font-cache-manager.ts         # 字体缓存管理器
│   │   ├── font-fallback-manager.ts      # 字体降级管理器
│   │   ├── dancing-script-test-suite.ts  # 测试套件
│   │   └── ...
│   ├── types/               # TypeScript类型定义
│   └── hooks/               # 自定义React hooks
├── public/                  # 静态资源
│   ├── fonts/               # 字体文件
│   │   ├── Dancing_Script/  # Dancing Script字体文件
│   │   └── ...
│   ├── images/              # 图片和图标
│   └── templates/           # 证书模板资源
├── docs/                    # 文档
└── scripts/                 # 构建和工具脚本
```

## 🎨 可用模板

- **优雅模板**: 适用于正式证书的专业设计
- **现代模板**: 适用于各种场合的当代风格
- **经典模板**: 传统证书布局
- **完成模板**: 专为课程完成证书设计

## 🛠️ 技术栈

- **框架**: Next.js 14 with App Router
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **PDF生成**: pdf-lib + fontkit
- **字体管理**: 自定义字体加载系统
- **状态管理**: React hooks
- **表单处理**: React Hook Form + Zod验证
- **图标**: Lucide React
- **字体**: Google Fonts + 本地TTF文件

## 📖 使用指南

### 基础使用

1. **选择模板**: 浏览并选择可用的证书模板
2. **自定义内容**: 填写收件人姓名、日期、详情和签名
3. **实时预览**: 查看证书的实时预览效果
4. **生成PDF**: 下载高质量PDF版本

### 字体管理

```typescript
import { UnifiedFontEmbedder } from '@/lib/unified-font-embedder';

// 创建字体嵌入器
const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);

// 使用降级策略嵌入字体（推荐）
const result = await fontEmbedder.embedFontWithFallback('Dancing Script', 400);

if (result.success) {
  // 使用字体绘制文本
  page.drawText('手写风格文本', {
    font: result.font,
    size: 24
  });
}
```

### 服务器端PDF生成

```typescript
import { generatePDFOnServer } from '@/lib/server-pdf-client';

const result = await generatePDFOnServer(template, data);

if (result.success && result.pdfBlob) {
  // 下载PDF
  const url = URL.createObjectURL(result.pdfBlob);
  // ... 下载逻辑
}
```

## 🧪 测试

访问 `/tests` 页面运行各种测试：

- **字体测试**: 验证Dancing Script字体加载和显示
- **性能测试**: 测试字体缓存和加载性能
- **降级测试**: 验证字体降级和错误处理
- **PDF生成测试**: 对比客户端和服务器端生成效果

## 🔧 配置

### 环境变量

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 字体配置

应用支持自定义字体。将字体文件添加到 `public/fonts/` 并在 `src/lib/fonts.ts` 中配置。

## 📦 构建生产版本

```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

## 🚀 部署

应用已优化用于Vercel部署，也可部署到任何支持Next.js的平台。

### Vercel部署

1. 将代码推送到GitHub
2. 连接仓库到Vercel
3. 使用默认设置部署

## 🤝 贡献

1. Fork仓库
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- Next.js团队提供的优秀框架
- Tailwind CSS的实用优先CSS框架
- pdf-lib的PDF生成功能
- 所有帮助改进项目的贡献者

## 📞 支持

如有问题或需要帮助，请：

1. 查看 [文档](docs/)
2. 搜索现有 [issues](../../issues)
3. 如需要可创建新issue

---

用 ❤️ 制作，由 Certificate Maker 团队
