# API 文档

本文档详细介绍了 Certificate Maker 的核心 API 和类。

## 核心类

### UnifiedFontEmbedder

统一字体嵌入器，整合了所有字体加载逻辑。

#### 构造函数

```typescript
constructor(pdfDoc: PDFDocument)
```

**参数:**
- `pdfDoc`: pdf-lib 的 PDFDocument 实例

#### 方法

##### embedFont()

```typescript
async embedFont(family: string, weight: number = 400): Promise<FontEmbedResult>
```

嵌入字体的基础方法。

**参数:**
- `family`: 字体族名称
- `weight`: 字体权重 (默认: 400)

**返回值:**
- `FontEmbedResult`: 字体嵌入结果

##### embedFontWithFallback() (推荐)

```typescript
async embedFontWithFallback(family: string, weight: number = 400): Promise<FontEmbedResult>
```

使用降级策略嵌入字体的推荐方法。

**参数:**
- `family`: 字体族名称
- `weight`: 字体权重 (默认: 400)

**返回值:**
- `FontEmbedResult`: 字体嵌入结果

##### embedFonts()

```typescript
async embedFonts(fontConfigs: FontConfig[]): Promise<Map<string, FontEmbedResult>>
```

批量嵌入字体。

**参数:**
- `fontConfigs`: 字体配置数组

**返回值:**
- `Map<string, FontEmbedResult>`: 字体嵌入结果映射

##### preloadProjectFonts()

```typescript
async preloadProjectFonts(): Promise<void>
```

预加载项目中使用的所有字体。

##### getCacheStats()

```typescript
getCacheStats(): CacheStats
```

获取字体缓存统计信息。

**返回值:**
- `CacheStats`: 缓存统计数据

### FontCacheManager

字体缓存管理器，提供智能缓存功能。

#### 静态方法

##### getInstance()

```typescript
static getInstance(): FontCacheManager
```

获取缓存管理器的单例实例。

#### 实例方法

##### get()

```typescript
get(family: string, weight: number, source: 'local' | 'google' = 'local'): ArrayBuffer | null
```

从缓存中获取字体数据。

##### set()

```typescript
set(family: string, weight: number, data: ArrayBuffer, source: 'local' | 'google' = 'local'): boolean
```

将字体数据存储到缓存。

##### preloadFont()

```typescript
async preloadFont(family: string, weight: number, source: 'local' | 'google' = 'local'): Promise<boolean>
```

预加载单个字体。

##### preloadFonts()

```typescript
async preloadFonts(fonts: Array<{ family: string; weight: number; source?: 'local' | 'google' }>): Promise<void>
```

批量预加载字体。

##### getStats()

```typescript
getStats(): CacheStats
```

获取缓存统计信息。

##### clear()

```typescript
clear(): void
```

清空所有缓存。

### FontFallbackManager

字体降级管理器，提供优雅的字体降级策略。

#### 构造函数

```typescript
constructor(pdfDoc: PDFDocument)
```

#### 方法

##### loadFontWithFallback()

```typescript
async loadFontWithFallback(family: string, weight: number = 400): Promise<FallbackResult>
```

使用降级策略加载字体。

##### addFallbackStrategy()

```typescript
addFallbackStrategy(strategy: FallbackStrategy): void
```

添加自定义降级策略。

##### testFallbackStrategy()

```typescript
async testFallbackStrategy(family: string, weight: number = 400): Promise<{
  strategy: FallbackStrategy;
  results: Array<{
    fallback: any;
    success: boolean;
    error?: string;
  }>;
}>
```

测试降级策略的有效性。

### DancingScriptTestSuite

Dancing Script 字体测试套件。

#### 方法

##### runFullTestSuite()

```typescript
async runFullTestSuite(): Promise<TestSuiteResult>
```

运行完整的测试套件。

**返回值:**
- `TestSuiteResult`: 测试结果汇总

## 类型定义

### FontEmbedResult

```typescript
interface FontEmbedResult {
  font: PDFFont;
  family: string;
  weight: number;
  isCustom: boolean;
  source: 'local' | 'google' | 'standard';
  fontName: string;
  success: boolean;
}
```

### FontConfig

```typescript
interface FontConfig {
  family: string;
  weight: number;
  style?: 'normal' | 'italic';
}
```

### CacheStats

```typescript
interface CacheStats {
  totalSize: number;
  totalFonts: number;
  hitRate: number;
  hits: number;
  misses: number;
}
```

### FallbackResult

```typescript
interface FallbackResult {
  success: boolean;
  font?: PDFFont;
  usedFallback: boolean;
  fallbackLevel: number;
  strategy: string;
  error?: string;
}
```

### TestSuiteResult

```typescript
interface TestSuiteResult {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: FontTestResult[];
  summary: string;
}
```

## 服务器端 API

### POST /api/generate-pdf

生成高质量PDF的服务器端API。

#### 请求体

```typescript
interface GeneratePDFRequest {
  templateId: string;
  data: CertificateData;
}
```

#### 响应

成功时返回PDF文件流，失败时返回错误信息。

#### 使用示例

```typescript
const response = await fetch('/api/generate-pdf', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    templateId: 'elegant-template-1',
    data: {
      recipientName: '张三',
      date: '2024-01-15',
      signature: '李四',
      details: '完成了高级课程学习'
    }
  })
});

if (response.ok) {
  const pdfBlob = await response.blob();
  // 处理PDF文件
}
```

## 客户端工具函数

### generatePDFOnServer()

```typescript
async function generatePDFOnServer(
  template: CertificateTemplate,
  data: CertificateData
): Promise<ServerPDFGenerationResult>
```

调用服务器端API生成PDF。

### downloadServerPDF()

```typescript
async function downloadServerPDF(
  template: CertificateTemplate,
  data: CertificateData,
  filename?: string
): Promise<boolean>
```

生成并下载服务器端PDF。

## 使用示例

### 基础字体嵌入

```typescript
import { UnifiedFontEmbedder } from '@/lib/unified-font-embedder';
import { PDFDocument } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';

// 创建PDF文档
const pdfDoc = await PDFDocument.create();
pdfDoc.registerFontkit(fontkit);

// 创建字体嵌入器
const fontEmbedder = new UnifiedFontEmbedder(pdfDoc);

// 嵌入字体
const result = await fontEmbedder.embedFontWithFallback('Dancing Script', 400);

if (result.success) {
  const page = pdfDoc.addPage();
  page.drawText('Hello World', {
    font: result.font,
    size: 24,
    x: 50,
    y: 500
  });
}
```

### 字体预加载

```typescript
// 预加载项目字体
await fontEmbedder.preloadProjectFonts();

// 获取缓存统计
const stats = fontEmbedder.getCacheStats();
console.log(`缓存命中率: ${(stats.hitRate * 100).toFixed(1)}%`);
```

### 测试字体系统

```typescript
import { DancingScriptTestSuite } from '@/lib/dancing-script-test-suite';

const testSuite = new DancingScriptTestSuite();
const results = await testSuite.runFullTestSuite();

console.log(`测试通过率: ${(results.passedTests / results.totalTests * 100).toFixed(1)}%`);
```

## 错误处理

所有API方法都包含适当的错误处理。建议在使用时检查返回结果：

```typescript
const result = await fontEmbedder.embedFontWithFallback('Dancing Script', 400);

if (!result.success) {
  console.error('字体加载失败，使用默认字体');
  // 处理错误情况
}
```

## 性能建议

1. **使用字体预加载**: 在应用启动时预加载常用字体
2. **监控缓存命中率**: 定期检查缓存性能
3. **使用降级策略**: 始终使用 `embedFontWithFallback()` 方法
4. **批量操作**: 使用 `embedFonts()` 进行批量字体加载
```
